import { useEffect } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination, Autoplay } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useHeroStore } from "../../../../store";
import { useImageCacheStore } from "../../../../store/imagesCacheStore";

export default function HeroSection() {
  const { heroData, isLoading, error, fetchHeroData } = useHeroStore();
  const { isLoaded, markAsLoaded } = useImageCacheStore();

  useEffect(() => {
    if (heroData.length === 0) {
      fetchHeroData();
    }
  }, []);

  useEffect(() => {
    heroData.forEach((slide) => {
      if (slide.image) {
        const img = new Image();
        img.src = slide.image;
        if (img.complete && img.naturalWidth !== 0) {
          markAsLoaded(slide.image);
        }
      }
    });
  }, [heroData]);

  const getTemporaryDescription = (title) => {
    const descriptions = {
      "Powerful Air Pumps":
        "Experience unmatched performance with our industrial-grade air pumps designed for continuous operation.",
      "Reliable Electric Motors":
        "Premium electric motors delivering consistent power and reliability for your industrial needs.",
      "Industrial Air Compressors":
        "High-capacity air compressors that meet the rigorous demands of modern industry.",
      default:
        "Discover our comprehensive range of industrial air solutions with quality engineering.",
    };
    return descriptions[title] || descriptions.default;
  };

  if (isLoading)
    return (
      <div className="relative h-[calc(100vh-var(--navbar-height))] w-full overflow-hidden bg-gray-200 animate-pulse" />
    );

  if (error)
    return (
      <div className="text-center text-red-600 py-8">
        Error loading hero content: {error}
      </div>
    );

  if (!heroData || heroData.length === 0)
    return <div className="text-center py-8">No data found</div>;

  return (
    <div className="relative h-[calc(100vh-var(--navbar-height))] w-full overflow-hidden">
      <Swiper
        modules={[Navigation, Pagination, Autoplay]}
        spaceBetween={0}
        slidesPerView={1}
        loop={true}
        autoplay={{
          delay: 5000,
          disableOnInteraction: false,
          pauseOnMouseEnter: true,
        }}
        navigation={{
          nextEl: ".hero-swiper-button-next",
          prevEl: ".hero-swiper-button-prev",
        }}
        pagination={{ clickable: true }}
        className="h-full w-full hero-swiper"
      >
        {heroData.map((slide, index) => {
          const loaded = isLoaded(slide.image);

          return (
            <SwiperSlide key={index}>
              <div className="relative w-full h-full">
                <img
                  src={slide.image || ""}
                  alt={slide.title || `Slide ${index + 1}`}
                  onLoad={() => {
                    if (slide.image) {
                      markAsLoaded(slide.image);
                    }
                  }}
                  className={`w-full h-full object-cover transition-all duration-700 ease-in-out ${
                    loaded
                      ? "blur-0 scale-100 opacity-100"
                      : "blur-md scale-105 opacity-0"
                  }`}
                />

                <div className="absolute inset-0 bg-gradient-to-r from-black/80 via-black/70 to-transparent" />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-black/20" />

                {loaded ? (
                  <div className="absolute inset-y-0 left-0 flex items-center px-5 sm:px-6 md:px-8 lg:px-16 xl:px-20">
                    <div className="max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg xl:max-w-xl text-white space-y-4 sm:space-y-5 md:space-y-6">
                      <h1 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-extrabold leading-tight text-white drop-shadow-xl">
                        {slide.title}
                      </h1>
                      <h2 className="text-sm sm:text-base md:text-lg lg:text-xl font-bold text-[var(--primary)]">
                        {slide.subtitle}
                      </h2>
                      <p className="text-xs sm:text-sm md:text-base font-medium leading-relaxed">
                        {slide.description ||
                          getTemporaryDescription(slide.title)}
                      </p>
                      <button className="bg-[var(--primary)] hover:bg-[var(--primary)]/90 text-white rounded-md px-4 py-2 text-xs sm:text-sm font-bold shadow-lg hover:scale-105 transition-transform duration-300">
                        Learn More
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="absolute inset-0 flex items-center justify-start px-8 md:px-26">
                    <div className="max-w-md w-full space-y-4">
                      <div className="w-72 h-10 bg-white/20 rounded-lg animate-pulse"></div>
                      <div className="w-56 h-6 bg-white/20 rounded-lg animate-pulse"></div>
                      <div className="w-80 h-4 bg-white/15 rounded-lg animate-pulse"></div>
                      <div className="w-64 h-4 bg-white/15 rounded-lg animate-pulse"></div>
                      <div className="w-24 h-8 bg-white/25 rounded-lg animate-pulse"></div>
                    </div>
                  </div>
                )}
              </div>
            </SwiperSlide>
          );
        })}
      </Swiper>

      {/* Navigation Arrows */}
      <div className="hidden sm:block hero-swiper-button-prev absolute left-1 sm:left-3 top-1/2 -translate-y-1/2 z-40">
        <div className="group flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 bg-black/30 hover:bg-black/50 backdrop-blur border border-white/20 hover:border-white/40 rounded-full transition-all">
          <ChevronLeft className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
        </div>
      </div>

      <div className="hidden sm:block hero-swiper-button-next absolute right-1 sm:right-3 top-1/2 -translate-y-1/2 z-40">
        <div className="group flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 bg-black/30 hover:bg-black/50 backdrop-blur border border-white/20 hover:border-white/40 rounded-full transition-all">
          <ChevronRight className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
        </div>
      </div>
    </div>
  );
}
