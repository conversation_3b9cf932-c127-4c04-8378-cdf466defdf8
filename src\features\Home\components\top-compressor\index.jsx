import { useState } from "react";
import bannerImg1 from "../../../../assets/images/factoryImg2.webp";
import { Check } from "lucide-react";

function TopCompressor() {
  const [isLoaded, setIsLoaded] = useState(false);

  return (
    <div className="bg-gray-100 py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center lg:text-left mb-8">
          <p
            className="font-semibold text-sm sm:text-base lg:text-xl uppercase tracking-wide mb-3 sm:mb-4"
            style={{ color: "var(--primary)" }}
          >
            Top Mining Equipment Manufacturer In China
          </p>
          <h1 className="text-lg text-black sm:text-2xl md:text-2xl lg:text-3xl font-bold py-4 leading-tight text-balance lg:text-pretty">
            One Stop Solution On Mining, Ore, Stone, Building Material Processing.
          </h1>
        </div>

        {/* Main Section */}
        <div className="flex flex-col lg:flex-row gap-6 lg:gap-8 xl:gap-12 lg:items-stretch">
          {/* Image with Blur & Skeleton */}
          <div className="w-full lg:w-1/2 relative">
            {!isLoaded && (
              <div className="absolute inset-0 bg-gray-300 animate-pulse rounded-lg z-10" />
            )}
            <img
              src={bannerImg1}
              alt="Factory manufacturing equipment"
              onLoad={() => setIsLoaded(true)}
              className={
                `w-full h-64 sm:h-80 lg:h-full object-cover rounded-lg shadow-2xl transition-all duration-700 ` +
                (!isLoaded ? "blur-md scale-105" : "blur-0")
              }
            />
          </div>

          {/* Text Section */}
          <div className="w-full lg:w-1/2 bg-white rounded-xl shadow-xl p-6 sm:p-8 lg:p-12 flex flex-col justify-between">
            <div>
              <p className="text-gray-600 text-sm sm:text-base mb-6 leading-relaxed">
                Sentai Machinery provides high quality and cost effective
                crushers, sand making machines, rotary dryer, rotary kiln and
                other customized mining machinery. Feel free to get a quote if you
                need them.
              </p>
              <h2 className="text-lg sm:text-xl font-semibold text-gray-800 mb-6 leading-tight">
                Providing Innovative Solution For Mining And Building Material
                Processing.
              </h2>
            </div>

            <ul className="space-y-3 sm:space-y-4">
              {[
                "We Use Quality Manufacturing Materials",
                "STCrushers Provide Unique Technology",
                "Group Of Certified & Experienced Team",
                "The Best Services Of Multiple Industries",
              ].map((text, index) => (
                <li className="flex items-start gap-3" key={index}>
                  <Check
                    className="w-5 h-5 text-white rounded-sm p-1 flex-shrink-0 mt-0.5"
                    style={{ backgroundColor: "var(--primary)" }}
                  />
                  <span className="text-gray-600 text-sm sm:text-base">{text}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}

export default TopCompressor;
