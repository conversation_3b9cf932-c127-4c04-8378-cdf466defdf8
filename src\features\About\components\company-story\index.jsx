import { Check, Award, Globe, Users, TrendingUp, Calendar, Star } from "lucide-react";
import product1Img from "../../../../assets/images/factoryImg.webp";

const ACHIEVEMENTS = [
  { icon: <Award className="w-4 h-4" />, text: "ISO Certified Quality", color: "bg-red-500" },
  { icon: <Globe className="w-4 h-4" />, text: "Global Shipping", color: "bg-orange-500" },
  { icon: <Users className="w-4 h-4" />, text: "24/7 Support", color: "bg-amber-500" },
  { icon: <TrendingUp className="w-4 h-4" />, text: "Custom Solutions", color: "bg-red-600" },
];

const STATS = [
  { number: "20+", label: "Years", sublabel: "of Excellence" },
  { number: "50+", label: "Countries", sublabel: "Served Globally" },
  { number: "500+", label: "Projects", sublabel: "Completed" },
];

export default function CompanyStory() {
  return (
    <div className="bg-gradient-to-br from-white via-gray-50 to-white py-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Background Decorations */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-red-500/5 to-orange-500/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-tr from-amber-500/5 to-red-500/5 rounded-full blur-2xl"></div>

      <div className="max-w-7xl mx-auto relative">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6 mb-16">
          <div className="flex-1">
            <p className="text-xs sm:text-sm uppercase text-red-500 tracking-wider font-medium mb-2">
              Our Story
            </p>
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold leading-tight text-gray-900">
              Two Decades of Innovation in Air Compression Technology
            </h2>
          </div>
          <div className="flex-1 lg:max-w-xl">
            <p className="text-sm sm:text-base text-gray-700 leading-relaxed">
              Founded in 2004, Pyramid Power has grown from a small manufacturing facility to a globally recognized leader in industrial air compression solutions.
            </p>
          </div>
        </div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Left Column */}
          <div className="space-y-8">
            <p className="text-sm sm:text-base text-gray-600 leading-relaxed">
              Today, we serve over 50 countries worldwide, providing cutting-edge air pumps, compressors, and custom pneumatic solutions that power industries across the globe.
            </p>

            {/* Achievements Grid */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-gray-100 shadow-lg">
              <h3 className="text-base sm:text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
                <Star className="w-5 h-5 text-red-500" />
                Key Achievements
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {ACHIEVEMENTS.map((item, index) => (
                  <div key={index} className="group flex items-center gap-3 p-3 rounded-xl hover:bg-red-50 transition">
                    <div className={`w-8 h-8 ${item.color} rounded-lg flex items-center justify-center text-white group-hover:scale-110 transition-transform`}>
                      {item.icon}
                    </div>
                    <span className="text-sm font-medium text-gray-700 group-hover:text-red-600 transition">
                      {item.text}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Column - Image */}
          <div className="relative">
            <div className="group relative overflow-hidden rounded-3xl shadow-2xl border-4 border-white">
              <img
                src={product1Img}
                alt="Manufacturing facility"
                className="w-full h-[400px] sm:h-[450px] object-cover transition-transform duration-500 group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"></div>

              {/* Floating Badge */}
              <div className="absolute top-5 right-5 bg-white/90 backdrop-blur-sm px-4 py-2 rounded-full shadow-lg">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-xs font-semibold text-gray-700">Active Since 2004</span>
                </div>
              </div>
            </div>

            {/* Stats: Now stacked under image on small screens */}
            <div className="mt-6 md:absolute md:-bottom-8 md:-left-8 grid grid-cols-1 sm:grid-cols-3 gap-4 w-full md:w-auto">
              {STATS.map((stat, index) => (
                <div key={index} className="bg-white p-4 rounded-2xl shadow-xl border border-gray-100 hover:shadow-2xl transition-all text-center">
                  <div className="text-xl font-bold text-red-500">{stat.number}</div>
                  <div className="text-sm font-semibold text-gray-700">{stat.label}</div>
                  <div className="text-xs text-gray-500">{stat.sublabel}</div>
                </div>
              ))}
            </div>

            {/* Decorative blurs */}
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-red-500/10 to-orange-500/10 rounded-full blur-xl"></div>
            <div className="absolute -bottom-4 -right-8 w-16 h-16 bg-gradient-to-br from-amber-500/10 to-red-500/10 rounded-full blur-lg"></div>
          </div>
        </div>
      </div>
    </div>
  );
}
