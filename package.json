{"name": "my-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.11", "axios": "^1.10.0", "daisyui": "^5.0.43", "lucide-react": "^0.523.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-lazy-load-image-component": "^1.6.3", "react-router-dom": "^7.6.2", "swiper": "^11.2.10", "tailwindcss": "^4.1.11", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "vite": "^7.0.0"}}