import { useEffect, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Autoplay } from "swiper/modules";
import { AlertCircle, ArrowRight, PackageX } from "lucide-react";
import { Link } from "react-router-dom";

import { useFeatureStore } from "../../../../store/homeApi/featureStore";
import bgImage from "../../../../assets/images/workerImg.webp";

import "swiper/css";
import "swiper/css/pagination";

// Product Image Component
const ProductImage = ({ src, alt }) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  useEffect(() => {
    if (!src) {
      setImageError(true);
      setImageLoaded(true);
      return;
    }

    const img = new Image();
    img.src = src;

    if (img.complete) {
      setImageLoaded(true);
    } else {
      img.onload = () => setImageLoaded(true);
      img.onerror = () => {
        setImageError(true);
        setImageLoaded(true);
      };
    }
  }, [src]);

  return (
    <div className="relative w-full h-40 sm:h-44 md:h-48 overflow-hidden">
      {!imageLoaded && (
        <div className="absolute inset-0 bg-gray-300 animate-pulse z-10" />
      )}
      {imageLoaded && (
        <img
          src={imageError ? bgImage : src}
          alt={alt}
          className="w-full h-full object-cover transition-all duration-300 hover:scale-105"
          loading="lazy"
        />
      )}
    </div>
  );
};

// Product Card Skeleton
const ProductSkeleton = () => (
  <div className="bg-white text-black rounded-xl shadow-md overflow-hidden h-full animate-pulse">
    <div className="w-full h-40 sm:h-44 md:h-48 bg-gray-300"></div>
    <div className="p-5 bg-white">
      <div className="h-6 bg-gray-300 rounded mb-3" />
      <div className="space-y-2 mb-4">
        <div className="h-4 bg-gray-200 rounded" />
        <div className="h-4 bg-gray-200 rounded" />
        <div className="h-4 bg-gray-200 rounded w-3/4" />
      </div>
      <div className="h-4 bg-gray-300 rounded w-20" />
    </div>
  </div>
);

// Error UI
const ErrorState = ({ error, onRetry }) => (
  <div className="max-w-screen-xl mx-auto px-4 py-16">
    <div className="bg-white rounded-xl shadow-md p-8 text-center">
      <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
      <h3 className="text-lg font-semibold text-gray-900 mb-2">
        Failed to load featured products
      </h3>
      <p className="text-gray-600 mb-4">{error}</p>
      <button
        onClick={onRetry}
        className="bg-[var(--primary)] text-white px-6 py-2 rounded-lg hover:opacity-90 transition"
      >
        Try Again
      </button>
    </div>
  </div>
);

// Empty UI
const EmptyState = () => (
  <div className="max-w-screen-xl mx-auto px-4 py-16">
    <div className="bg-white rounded-xl shadow-md p-8 text-center">
      <PackageX className="w-12 h-12 text-gray-400 mx-auto mb-4" />
      <h3 className="text-lg font-semibold text-gray-900 mb-2">
        No Featured Products
      </h3>
      <p className="text-gray-600">
        No featured products are currently available.
      </p>
    </div>
  </div>
);

export default function FeaturedProducts() {
  const { featureData, isLoading, error, fetchFeatureData } = useFeatureStore();

  useEffect(() => {
    fetchFeatureData();
  }, [fetchFeatureData]);

  const displayData = featureData || [];
  const hasEnoughSlidesForLoop = displayData.length > 4;

  return (
    <section>
      {/* Header */}
      <div
        className="relative pt-12 pb-48 px-4 sm:px-6 md:px-12 lg:px-20 text-white"
        style={{
          backgroundImage: `linear-gradient(rgba(28, 26, 48, 0.85), rgba(28, 26, 48, 0.85)), url(${bgImage})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        <div className="max-w-screen-xl mx-auto flex flex-col lg:flex-row gap-6 lg:gap-8 items-start lg:items-center justify-between">
          <div className="flex-1">
            <p className="text-xs sm:text-sm uppercase text-red-500 tracking-wider font-medium mb-2">
              Featured Products
            </p>
            <h2 className="text-3xl lg:text-4xl font-bold leading-tight">
              High Quality Mining Equipment <br className="hidden sm:block" />
              For Many Applications.
            </h2>
          </div>
          <div className="flex-1 lg:max-w-2xl">
            <p className="text-base text-gray-300 leading-relaxed">
              Professional mining equipment for stone crushing, mining
              separating, ore beneficiation, drying, briquette making, sand
              making and more.
            </p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="relative z-10 -mt-40">
        <div className="max-w-screen-xl mx-auto px-4 sm:px-6 md:px-12 lg:px-20">
          {isLoading && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {[...Array(4)].map((_, i) => (
                <ProductSkeleton key={i} />
              ))}
            </div>
          )}

          {!isLoading && error && (
            <ErrorState error={error} onRetry={fetchFeatureData} />
          )}

          {!isLoading && !error && displayData.length > 0 && (
            <Swiper
              modules={[Pagination, Autoplay]}
              loop={hasEnoughSlidesForLoop}
              autoplay={
                hasEnoughSlidesForLoop
                  ? {
                      delay: 3000,
                      disableOnInteraction: false,
                      pauseOnMouseEnter: false,
                    }
                  : false
              }
              pagination={{ clickable: true }}
              spaceBetween={24}
              breakpoints={{
                320: { slidesPerView: 1 },
                768: { slidesPerView: Math.min(2, displayData.length) },
                1024: { slidesPerView: Math.min(4, displayData.length) },
              }}
              className="products-swiper"
            >
              {displayData.map((product) => (
                <SwiperSlide key={product.id || product.slug} >
                  <div className="bg-white  text-black rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 h-full">
                    <ProductImage src={product.image} alt={product.name} />
                    <div className="p-5 bg-white">
                      <h4 className="font-semibold text-lg mb-2 line-clamp-2">
                        {product.name}
                      </h4>
                      <p className="text-sm text-gray-600 leading-relaxed line-clamp-3 min-h-[4rem]">
                        {product.summary}
                      </p>

                      {product.slug && (
                        <Link
                          to={`/products/${product.slug}`}
                          className="inline-flex items-center gap-1 text-[var(--primary)] font-medium mt-4 text-sm hover:underline"
                        >
                          Read More <ArrowRight size={16} />
                        </Link>
                      )}
                    </div>
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>
          )}

          {!isLoading && !error && displayData.length === 0 && <EmptyState />}
        </div>
      </div>
    </section>
  );
}
