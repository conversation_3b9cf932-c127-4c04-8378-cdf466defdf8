import { useNavigate } from "react-router-dom";

export default function RelatedProducts({ product }) {
  const navigate = useNavigate();
  const relatedProducts = product.related_products;

  if (!relatedProducts || relatedProducts.length === 0) {
    return null;
  }

  const handleProductClick = (productSlug) => {
    navigate(`/products/${productSlug}`);
  };

  return (
    <section className="bg-slate-200 border-t border-gray-200 px-6 md:px-20 py-12">
      <div className="max-w-7xl mx-auto">
        {/* Simple Header */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            Related Products
          </h2>
          <div className="w-16 h-1 bg-[var(--primary)] rounded"></div>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {relatedProducts.map((relatedProduct, index) => (
            <div
              key={index}
              className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200 cursor-pointer"
              onClick={() => handleProductClick(relatedProduct.slub)}
            >
              {/* Product Image */}
              <div className="aspect-w-16 aspect-h-12 bg-gray-100">
                {relatedProduct.image ? (
                  <img
                    src={relatedProduct.image}
                    alt={relatedProduct.name}
                    className="w-full h-48 object-cover hover:scale-105 transition-transform duration-300"
                    loading="lazy"
                  />
                ) : (
                  <div className="w-full h-48 bg-gray-200 flex items-center justify-center">
                    <div className="text-gray-500 text-center">
                      <div className="text-4xl mb-2 opacity-50">📦</div>
                      <p className="text-sm font-medium">No Image Available</p>
                    </div>
                  </div>
                )}
              </div>
              
              {/* Product Info */}
              <div className="p-4">
                <h3 className="text-lg font-bold text-gray-900 mb-2 hover:text-[var(--primary)] transition-colors">
                  {relatedProduct.name}
                </h3>
                <p className="text-gray-600 text-sm mb-4">
                  High-performance industrial air pump for professional applications
                </p>

                {/* Bottom section */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span className="text-xs text-gray-600">In Stock</span>
                  </div>
                  <div className="flex items-center gap-1 text-[var(--primary)] text-sm font-medium">
                    <span>View Details</span>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="mt-8 text-center">
          <button className="bg-[var(--primary)] text-white px-6 py-3 rounded-lg font-semibold hover:bg-red-700 transition duration-200">
            View All Products
          </button>
        </div>
      </div>
    </section>
  );
}
