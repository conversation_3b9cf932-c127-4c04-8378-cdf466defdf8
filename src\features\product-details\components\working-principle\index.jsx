import { ChevronDown } from "lucide-react";
import {  useState } from "react";

export default function WorkingPrinciple({ product }) {
  const workingPrincipleData = product.working_principle;
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  if (!workingPrincipleData) return null;


  const steps = [
    {
      step: "01",
      title: "Air Intake",
      desc: "Air enters through the inlet valve during the intake stroke",
    },
    {
      step: "02",
      title: "Compression",
      desc: "<PERSON><PERSON> compresses the air, increasing pressure and reducing volume",
    },
    {
      step: "03",
      title: "Discharge",
      desc: "Compressed air is expelled through the outlet valve",
    },
  ];

  return (
    <section className="bg-slate-200 border-t  border-gray-200 px-4 sm:px-6 md:px-10 lg:px-20 py-12">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            Working Principle
          </h2>
          <div className="w-16 h-1 bg-[var(--primary)] rounded" />
        </div>

        {/* Description */}
        <div className="mb-12">
          <div className="bg-white rounded-lg p-6">
            <p className="text-gray-700 text-base leading-relaxed mb-6">
              {workingPrincipleData.text}
            </p>

            <div className="flex flex-wrap gap-2">
              {["High Efficiency", "Reliable Operation", "Low Maintenance"].map(
                (feature, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 bg-white text-[var(--primary)] text-sm font-medium rounded border border-[var(--primary)]/20"
                  >
                    <div className="w-1.5 h-1.5 bg-[var(--primary)] rounded-full mr-2" />
                    {feature}
                  </span>
                )
              )}
            </div>
          </div>
        </div>

        {/* Content Section */}
        <div
          className={`flex flex-col gap-8 ${
            workingPrincipleData.image
              ? "lg:flex-row items-start"
              : "items-center"
          }`}
        >
          {workingPrincipleData.image && (
            <div className="w-full lg:w-1/2  rounded-lg p-4 flex justify-center">
              {/* 👇 Set relative + min-height on container */}
              <div className="w-full max-w-md relative min-h-[24rem] h-1/2">
                {/* Skeleton */}
                {!imageLoaded && (
                  <div className="absolute inset-0 bg-gray-200 animate-pulse rounded shadow-sm z-10 h-full" />
                )}

                {/* Image */}
                <img
                  src={workingPrincipleData.image}
                  alt="Working Principle Diagram"
                  onLoad={() => setImageLoaded(true)}
                  onError={() => {
                    setImageError(true);
                    setImageLoaded(true);
                  }}
                  className={`w-full h-full object-contain rounded shadow-sm transition-opacity duration-300 ${
                    imageLoaded ? "opacity-100" : "opacity-0"
                  }`}
                  loading="lazy"
                />

                {/* Label */}
                <div className="absolute -bottom-12 w-full text-center">
                  <p className="text-sm text-gray-600 font-medium mt-4">
                    Technical Diagram
                  </p>
                </div>
              </div>
            </div>
          )}
          {/* Process Flow Section */}
          <div
            className={`w-full ${
              workingPrincipleData.image ? "lg:w-1/2" : "max-w-2xl"
            }`}
          >
            <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center lg:text-left">
              Process Flow
            </h3>

            <div className="bg-gray-50 rounded-lg p-4 flex flex-col justify-between">
              {steps.map((item, index) => (
                <div key={index} className="flex flex-col items-center">
                  <div className="bg-white rounded-lg p-4 text-center w-full">
                    <div className="inline-flex items-center justify-center w-12 h-12 bg-[var(--primary)] rounded-full text-white font-bold text-lg mb-3">
                      {item.step}
                    </div>
                    <h4 className="text-lg font-bold text-gray-900 mb-2">
                      {item.title}
                    </h4>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {item.desc}
                    </p>
                  </div>

                  {index < steps.length - 1 && (
                    <div className="my-3">
                      <ChevronDown className="text-[var(--primary)] w-5 h-5" />
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
