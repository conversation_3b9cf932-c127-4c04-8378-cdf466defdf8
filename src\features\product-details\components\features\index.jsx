import {
  Zap,
  Shield,
  Gauge,
  <PERSON><PERSON>s,
  Award,
  CheckCircle,
  Star,
  TrendingUp,
  Wrench,
  Target,
  Clock,
  Battery,
  Thermometer,
  Wind,
  Volume2,
  Cpu,
  Trophy,
  Rocket,
  Globe,
} from "lucide-react";

export default function ProductFeatures({ product }) {
  const features = product.features;

  // Icon mapping for different feature types
  const getFeatureIcon = (feature, index) => {
    const featureText = (
      typeof feature === "string"
        ? feature
        : feature.title || feature.name || ""
    ).toLowerCase();

    // Smart icon selection based on feature content
    if (
      featureText.includes("power") ||
      featureText.includes("energy") ||
      featureText.includes("electric")
    ) {
      return <Zap className="w-6 h-6" />;
    } else if (
      featureText.includes("safe") ||
      featureText.includes("protect") ||
      featureText.includes("security")
    ) {
      return <Shield className="w-6 h-6" />;
    } else if (
      featureText.includes("speed") ||
      featureText.includes("fast") ||
      featureText.includes("quick")
    ) {
      return <Gauge className="w-6 h-6" />;
    } else if (
      featureText.includes("control") ||
      featureText.includes("adjust") ||
      featureText.includes("setting")
    ) {
      return <Settings className="w-6 h-6" />;
    } else if (
      featureText.includes("quality") ||
      featureText.includes("premium") ||
      featureText.includes("award")
    ) {
      return <Award className="w-6 h-6" />;
    } else if (
      featureText.includes("reliable") ||
      featureText.includes("durable") ||
      featureText.includes("stable")
    ) {
      return <CheckCircle className="w-6 h-6" />;
    } else if (
      featureText.includes("performance") ||
      featureText.includes("efficient") ||
      featureText.includes("optimize")
    ) {
      return <TrendingUp className="w-6 h-6" />;
    } else if (
      featureText.includes("maintain") ||
      featureText.includes("service") ||
      featureText.includes("repair")
    ) {
      return <Wrench className="w-6 h-6" />;
    } else if (
      featureText.includes("precise") ||
      featureText.includes("accurate") ||
      featureText.includes("target")
    ) {
      return <Target className="w-6 h-6" />;
    } else if (
      featureText.includes("time") ||
      featureText.includes("timer") ||
      featureText.includes("schedule")
    ) {
      return <Clock className="w-6 h-6" />;
    } else if (
      featureText.includes("battery") ||
      featureText.includes("charge") ||
      featureText.includes("portable")
    ) {
      return <Battery className="w-6 h-6" />;
    } else if (
      featureText.includes("temperature") ||
      featureText.includes("heat") ||
      featureText.includes("cool")
    ) {
      return <Thermometer className="w-6 h-6" />;
    } else if (
      featureText.includes("air") ||
      featureText.includes("flow") ||
      featureText.includes("ventil")
    ) {
      return <Wind className="w-6 h-6" />;
    } else if (
      featureText.includes("quiet") ||
      featureText.includes("silent") ||
      featureText.includes("noise")
    ) {
      return <Volume2 className="w-6 h-6" />;
    } else if (
      featureText.includes("smart") ||
      featureText.includes("digital") ||
      featureText.includes("auto")
    ) {
      return <Cpu className="w-6 h-6" />;
    } else {
      // Fallback icons based on index
      const fallbackIcons = [
        <Star className="w-6 h-6" />,
        <Trophy className="w-6 h-6" />,
        <Rocket className="w-6 h-6" />,
        <Globe className="w-6 h-6" />,
      ];
      return fallbackIcons[index % fallbackIcons.length];
    }
  };

  return (
    <section className="bg-slate-200 border-t border-gray-200 px-4 sm:px-6 md:px-10 lg:px-20 py-12">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            Key Features
          </h2>
          <div className="w-16 h-1 bg-[var(--primary)] rounded" />
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {features.map((feature, idx) => (
            <div
              key={idx}
              className="bg-white rounded-lg p-6 shadow-sm border border-gray-200 hover:shadow-md transition duration-200"
            >
              <div className="flex items-start gap-4">
                {/* Feature Icon */}
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-[var(--primary)] rounded-lg flex items-center justify-center">
                    <div className="text-white">
                      {getFeatureIcon(feature, idx)}
                    </div>
                  </div>
                </div>

                {/* Feature Content */}
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">
                    {typeof feature === "string"
                      ? feature
                      : feature.title || feature.name}
                  </h3>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {typeof feature === "string"
                      ? "This feature enhances product performance and reliability."
                      : feature.description ||
                        "Feature description not available."}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Feature Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[
            {
              icon: <Trophy className="w-5 h-5" />,
              title: "Industry Leading",
              desc: "Best-in-class performance standards",
            },
            {
              icon: <Shield className="w-5 h-5" />,
              title: "Proven Reliability",
              desc: "Tested in demanding environments",
            },
            {
              icon: <Rocket className="w-5 h-5" />,
              title: "Future Ready",
              desc: "Built for tomorrow's challenges",
            },
          ].map((summary, idx) => (
            <div
              key={idx}
              className="bg-white rounded-lg p-4 text-center border border-gray-200"
            >
              <div className="inline-flex items-center justify-center w-10 h-10 bg-[var(--primary)] rounded-full text-white mb-3">
                {summary.icon}
              </div>
              <h4 className="text-lg font-bold text-gray-900 mb-2">
                {summary.title}
              </h4>
              <p className="text-gray-600 text-sm">{summary.desc}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
