import { create } from "zustand";
import axios from "axios"; 
import apiClient from "../../api/axiosConfig.js";

export const useSearchStore = create((set, get) => ({
  // State
  searchResults: [],
  searchQuery: "",
  isSearching: false,
  searchError: null,
  hasSearched: false,

  // Pagination for search results
  currentPage: 1,
  totalPages: 1,
  perPage: 12,

  abortController: null,
  debounceTimer: null,

  // Search products
  searchProducts: async (query, page = 1) => {
    if (!query.trim()) {
      set({
        searchResults: [],
        searchQuery: "",
        hasSearched: false,
        searchError: null,
      });
      return;
    }

    const state = get();

    // Cancel previous request if exists
    if (state.abortController) {
      state.abortController.abort();
    }

    // Create new abort controller
    const abortController = new AbortController();
    set({
      isSearching: true,
      searchError: null,
      abortController,
      searchQuery: query.trim(),
    });

    try {
      const params = new URLSearchParams();
      params.append("query", query.trim());
      params.append("page", page);
      params.append("per_page", state.perPage);

      const response = await apiClient.get(`/api/products/search?${params.toString()}`, {
        signal: abortController.signal,
      });

      const { items, pagination } = response.data.data.products;

      if (!abortController.signal.aborted) {
        set({
          searchResults: items,
          currentPage: pagination.current_page,
          totalPages: pagination.last_page,
          hasSearched: true,
          isSearching: false,
          searchError: null,
          abortController: null,
        });
      }
    } catch (error) {
      if (axios.isCancel(error) || error.name === "AbortError") {
        // This request was cancelled; do nothing special
        return;
      }

      console.error("Failed to search products:", error);
      set({
        searchResults: [],
        isSearching: false,
        searchError: error.response?.data?.message || error.message || "Search failed",
        abortController: null,
      });
    }
  },

  // Debounced search
  debouncedSearch: (query, delay = 300) => {
    const state = get();

    if (state.debounceTimer) {
      clearTimeout(state.debounceTimer);
    }

    const timer = setTimeout(() => {
      if (query.trim()) {
        get().searchProducts(query.trim());
      } else {
        get().clearSearch();
      }
    }, delay);

    set({ debounceTimer: timer });
  },

  // Clear search
  clearSearch: () => {
    const state = get();

    if (state.abortController) {
      state.abortController.abort();
    }

    if (state.debounceTimer) {
      clearTimeout(state.debounceTimer);
    }

    set({
      searchResults: [],
      searchQuery: "",
      hasSearched: false,
      isSearching: false,
      searchError: null,
      currentPage: 1,
      totalPages: 1,
      abortController: null,
      debounceTimer: null,
    });
  },

  // Set search query without triggering search (for controlled inputs)
  setSearchQuery: (query) => {
    set({ searchQuery: query });
  },
}));
