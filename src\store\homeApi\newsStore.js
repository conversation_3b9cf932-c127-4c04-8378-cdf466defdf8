import { create } from 'zustand';
import apiClient from '../../api/axiosConfig.js';

export const useNewsStore = create((set) => ({
  // State
  newsData: [],
  isLoading: false,
  error: null,

  // Actions
  fetchNewsData: async () => {
    set({ isLoading: true, error: null });
    
    try {
      // Now just use the endpoint path, base URL is already configured
      const response = await apiClient.get('/api/home-news');
      console.log(response);
      // Extract the data array from the response
      const data = response.data.data || [];
      
      set({ 
        newsData: data,
        isLoading: false,
        error: null 
      });
      
      return data;
    } catch (error) {
      console.error('Error fetching newsData:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch news data';
      
      set({ 
        newsData: [],
        isLoading: false,
        error: errorMessage 
      });
      throw error;
    }
  },

  // Clear error
  clearError: () => set({ error: null }),

  // Reset store
  reset: () => set({ 
    newsData: [],
    isLoading: false,
    error: null 
  }),
}));
