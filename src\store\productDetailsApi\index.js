import { create } from 'zustand';

const useProductDetailsStore = create((set) => ({
  product: null,
  loading: false,
  error: null,
  
  fetchProductDetails: async (productSlug) => {
    set({ loading: true, error: null });
    
    try {
      // Use environment variable or fallback to localhost
      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000';
      const response = await fetch(`${apiBaseUrl}/api/products/${productSlug}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch product: ${response.status}`);
      }
      
      const result = await response.json();
      set({ product: result.data, loading: false, error: null });
    } catch (error) {
      console.error('Error fetching product details:', error);
      set({ 
        product: null, 
        loading: false, 
        error: error.message || 'Failed to fetch product details' 
      });
    }
  },
  
  clearProduct: () => {
    set({ product: null, error: null, loading: false });
  }
}));

export default useProductDetailsStore;
