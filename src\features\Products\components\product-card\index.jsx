import { useState } from "react";
import { ArrowRight, Zap, Gauge, PackageCheck, Cpu, Info } from "lucide-react";
import { Link } from "react-router-dom";
import { useProductsStore } from "../../../../store/productsApi";

// Optional icons for known fields
const specIcons = {
  power: <Zap className="w-4 h-4 text-indigo-500" />,
  performance: <Gauge className="w-4 h-4 text-green-500" />,
  stock: <PackageCheck className="w-4 h-4 text-yellow-600" />,
  processor: <Cpu className="w-4 h-4 text-pink-500" />,
  default: <Info className="w-4 h-4 text-gray-400" />,
};

export default function ProductCard({ product, category }) {
  const image = product.media || product.image || "/placeholder.jpg";
  const [isImgLoaded, setIsImgLoaded] = useState(false);

  const getCategorySpecs = useProductsStore((state) => state.getCategorySpecs);
  const categorySpecs = getCategorySpecs(category);

  const specs = categorySpecs.map((spec) => ({
    ...spec,
    value: product[spec.field],
    icon: specIcons[spec.field] || specIcons.default,
  }));

  return (
    <div className="group bg-white rounded-2xl shadow-md hover:shadow-xl border border-gray-100 hover:border-gray-200 transition-all duration-300 overflow-hidden">
      {/* Image */}
      <div className="overflow-hidden relative w-full h-52 bg-gray-100">
        {!isImgLoaded && (
          <div className="absolute inset-0 animate-pulse bg-gray-200" />
        )}
        <img
          src={image}
          alt={product.name}
          loading="lazy"
          onLoad={() => setIsImgLoaded(true)}
          className={`w-full h-full object-cover transition-transform duration-300 group-hover:scale-105 ${
            isImgLoaded ? "opacity-100" : "opacity-0"
          }`}
        />
      </div>

      {/* Content */}
      <div className="p-5 flex flex-col gap-4">
        {/* Title */}
        <h3 className="text-lg font-bold text-gray-900 group-hover:text-[var(--primary)] transition-colors leading-snug">
          {product.name}
        </h3>

        {/* Description */}
        <p className="text-sm text-gray-600 leading-relaxed line-clamp-3 min-h-[48px]">
          {/* {product.description?.slice(0, 120)}... */}
          {product.description}
        </p>

        {/* Specs */}
        <div className="space-y-2">
          {specs.slice(0, 3).map((spec, index) => (
            <div
              key={index}
              className="flex items-center justify-between text-sm px-3 py-1.5 bg-gray-50 rounded-md border border-gray-100"
            >
              <div className="flex items-center gap-2 text-gray-700">
                {spec.icon}
                <span className="font-medium">{spec.label}</span>
              </div>
              <span className="text-gray-900 font-semibold">
                {spec.value ? `${spec.value}${spec.unit ? ` ${spec.unit}` : ""}` : "N/A"}
              </span>
            </div>
          ))}
        </div>

        {/* Status + Link Row */}
        <div className="flex items-center justify-between mt-2">
          <span
            className={`text-xs font-semibold px-2.5 py-1 rounded-full tracking-wide ${
              product.status === "available"
                ? "bg-gradient-to-r from-green-400 to-green-600 text-white"
                : "bg-gray-200 text-gray-700"
            }`}
          >
            {product.status?.toUpperCase() || "UNKNOWN"}
          </span>

          <Link
            to={`/products/${product.slug}`}
            className="inline-flex items-center gap-1 text-sm font-medium text-[var(--primary)] hover:underline transition"
          >
            Read More <ArrowRight size={14} />
          </Link>
        </div>
      </div>
    </div>
  );
}
