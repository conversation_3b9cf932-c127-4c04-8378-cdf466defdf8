import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Home from './pages/Home.jsx';
import About from './pages/About.jsx';
import Products from './pages/Products.jsx';
import ProductDetails from './pages/ProductDetails.jsx';
import Header from './layout/Header/index.jsx';
import ScrollToTop from './components/ScrollToTop.jsx';
import News from './pages/News.jsx';

export default function App() {
  return (
    <Router>
      <div >
        <ScrollToTop />
        <Header/>
        <main>
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/about" element={<About />} />
            <Route path="/products" element={<Products/>}  />
            <Route path="/products/:id" element={<ProductDetails />} />
            <Route path='/news' element={<News/>}/>
          </Routes>
        </main>
      </div>
    </Router>
  );
}
