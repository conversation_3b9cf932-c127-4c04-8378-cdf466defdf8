import { Calendar, Trophy, Globe, Award, Rocket } from "lucide-react";

const COMPANY_TIMELINE = [
  {
    year: "2004",
    title: "Company Founded",
    desc: "Started as a small air pump manufacturer in Dubai with a vision to revolutionize industrial air solutions",
    icon: <Rocket className="w-6 h-6" />,
    color: "from-red-500 to-red-600",
  },
  {
    year: "2010",
    title: "International Expansion",
    desc: "Expanded operations to serve global markets across 25+ countries with innovative products",
    icon: <Globe className="w-6 h-6" />,
    color: "from-gray-600 to-gray-700",
  },
  {
    year: "2015",
    title: "ISO Certification",
    desc: "Achieved ISO quality standards and certifications, establishing ourselves as a trusted industry leader",
    icon: <Award className="w-6 h-6" />,
    color: "from-slate-600 to-slate-700",
  },
  {
    year: "2024",
    title: "Industry Leader",
    desc: "Recognized as a leading air compression solutions provider with cutting-edge technology and innovation",
    icon: <Trophy className="w-6 h-6" />,
    color: "from-red-600 to-red-700",
  },
];

export default function Timeline() {
  return (
    <div className="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 py-12 px-4 sm:px-6 md:px-8 lg:px-12 relative overflow-hidden">
      {/* Decorations */}
      <div className="absolute top-0 left-0 w-32 h-32 bg-gradient-to-br from-red-500/10 to-red-600/10 rounded-full blur-2xl"></div>
      <div className="absolute bottom-0 right-0 w-32 h-32 bg-gradient-to-tr from-gray-500/10 to-slate-500/10 rounded-full blur-2xl"></div>

      <div className="max-w-6xl mx-auto relative">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 px-3 py-1.5 bg-red-500/20 border border-red-500/30 rounded-full mb-4">
            <Calendar className="w-4 h-4 text-red-400" />
            <p className="font-semibold text-sm uppercase tracking-wide text-red-400">Our Journey</p>
          </div>
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-4">
            Milestones That Define Our
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-red-400 to-red-500">
              Success Story
            </span>
          </h2>
          <p className="text-base sm:text-lg text-gray-300 max-w-2xl mx-auto">
            From humble beginnings to global leadership — discover the key moments that shaped our journey.
          </p>
        </div>

        {/* Vertical Line */}
        <div className="relative">
          <div className="absolute left-1/2 transform -translate-x-1/2 top-0 bottom-0 w-1 bg-gradient-to-b from-red-500 via-gray-500 to-slate-500 rounded-full z-0 hidden md:block"></div>

          <div className="flex flex-col gap-14">
            {COMPANY_TIMELINE.map((item, index) => (
              <div key={index} className="relative flex flex-col md:flex-row md:items-center gap-6">
                {/* Icon Node */}
                <div className="md:absolute md:left-1/2 md:transform md:-translate-x-1/2 z-10 flex justify-center">
                  <div className={`w-12 h-12 bg-gradient-to-br ${item.color} text-white rounded-full flex items-center justify-center shadow-md border-4 border-gray-900`}>
                    {item.icon}
                  </div>
                </div>

                {/* Left Card */}
                {index % 2 === 0 ? (
                  <>
                    <div className="w-full md:w-1/2 md:pr-10 flex justify-end">
                      <div className="bg-white/5 border border-white/10 rounded-xl p-5 backdrop-blur-md text-left max-w-md w-full hover:shadow-xl hover:bg-white/10">
                        <div className={`inline-flex items-center gap-2 px-3 py-1.5 bg-gradient-to-r ${item.color} text-white text-xs font-bold rounded-full mb-3 shadow`}>
                          <Calendar className="w-4 h-4" />
                          {item.year}
                        </div>
                        <h3 className="text-white text-lg sm:text-xl font-bold mb-2">{item.title}</h3>
                        <p className="text-gray-300 text-sm sm:text-base leading-relaxed">{item.desc}</p>
                        <div className={`w-16 h-1 bg-gradient-to-r ${item.color} rounded-full mt-4 ml-auto`}></div>
                      </div>
                    </div>
                    <div className="hidden md:block w-1/2"></div>
                  </>
                ) : (
                  <>
                    <div className="hidden md:block w-1/2"></div>
                    <div className="w-full md:w-1/2 md:pl-10 flex justify-start">
                      <div className="bg-white/5 border border-white/10 rounded-xl p-5 backdrop-blur-md text-left max-w-md w-full hover:shadow-xl hover:bg-white/10">
                        <div className={`inline-flex items-center gap-2 px-3 py-1.5 bg-gradient-to-r ${item.color} text-white text-xs font-bold rounded-full mb-3 shadow`}>
                          <Calendar className="w-4 h-4" />
                          {item.year}
                        </div>
                        <h3 className="text-white text-lg sm:text-xl font-bold mb-2">{item.title}</h3>
                        <p className="text-gray-300 text-sm sm:text-base leading-relaxed">{item.desc}</p>
                        <div className={`w-16 h-1 bg-gradient-to-r ${item.color} rounded-full mt-4`}></div>
                      </div>
                    </div>
                  </>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* CTA */}
        <div className="text-center mt-16">
          <button className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-semibold rounded-full shadow hover:shadow-red-500/25 transition-all duration-300 text-sm sm:text-base">
            <Trophy className="w-5 h-5" />
            Discover Our Full Story
          </button>
        </div>
      </div>
    </div>
  );
}
