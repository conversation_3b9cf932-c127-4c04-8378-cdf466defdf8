import { create } from "zustand";
import { persist } from "zustand/middleware";
import apiClient from "../../api/axiosConfig.js";

export const useProductsStore = create(
  persist(
    (set, get) => ({
      products: [],
      currentPage: 1,
      totalPages: 1,
      perPage: 12,
      isLoading: false,
      error: null,

      // Data freshness
      lastFetchTime: null,
      dataFreshDuration: 5 * 60 * 1000, // 5 minutes

      abortController: null,
      debounceTimer: null,

      filters: {},
      activeFilters: {},

      // Category selection
      selectedCategory: "air-pumps",
      categories: [
        { value: "air-compressors", label: "Air Compressors" },
        { value: "electric-motors", label: "Electric Motors" },
        { value: "air-pumps", label: "Air Pumps" },
      ],

      // Category-specific filter configurations
      categoryFilters: {
        "air-compressors": ["power", "tank_capacity", "max_pressure"],
        "electric-motors": ["output", "speed", "efficiency"],
        "air-pumps": ["rpm", "displacement", "motor_hp"],
      },

      // Category-specific specs display configuration
      categorySpecs: {
        "air-compressors": [
          { label: "Power", field: "power", unit: "kW" },
          { label: "Tank Capacity", field: "tank_capacity", unit: "L" },
          { label: "Max Pressure", field: "max_pressure", unit: "bar" },
          { label: "Status", field: "status", unit: "" },
        ],
        "electric-motors": [
          { label: "Output", field: "output", unit: "kW" },
          { label: "Speed", field: "speed", unit: "RPM" },
          { label: "Efficiency", field: "efficiency", unit: "%" },
          { label: "Status", field: "status", unit: "" },
        ],
        "air-pumps": [
          { label: "RPM", field: "rpm", unit: "" },
          { label: "Displacement", field: "displacement", unit: "L/min" },
          { label: "Motor HP", field: "motor_hp", unit: "HP" },
          { label: "Status", field: "status", unit: "" },
        ],
      },

      getActiveFilterKeys: () => {
        const state = get();
        return state.categoryFilters[state.selectedCategory] || [];
      },

      getCategorySpecs: (category) => {
        const state = get();
        return state.categorySpecs[category] || [];
      },

      isDataFresh: () => {
        const state = get();
        if (!state.lastFetchTime) return false;
        return Date.now() - state.lastFetchTime < state.dataFreshDuration;
      },

      smartFetchProducts: async (
        page = 1,
        customFilters = null,
        forceRefresh = false
      ) => {
        const state = get();
        const activeFilters = customFilters || state.activeFilters;

        if (!forceRefresh && state.isDataFresh() && state.products.length > 0) {
          const filtersMatch =
            JSON.stringify(activeFilters) ===
            JSON.stringify(state.activeFilters);
          const pageMatch = page === state.currentPage;
          if (filtersMatch && pageMatch) {
            console.log("Using cached products data");
            return;
          }
        }

        return get().fetchProducts(page, customFilters);
      },

      fetchProducts: async (page = 1, customFilters = null) => {
        const state = get();

        if (state.abortController) {
          console.log("Cancelling previous request");
          state.abortController.abort();
        }

        const abortController = new AbortController();
        set({ isLoading: true, error: null, abortController });

        try {
          const activeFilters = customFilters || state.activeFilters;

          const params = new URLSearchParams();
          params.append("product_type", "product");
          params.append("category", state.selectedCategory);
          params.append("page", page);
          params.append("per_page", state.perPage);

          get()
            .getActiveFilterKeys()
            .forEach((key) => {
              activeFilters[key]?.forEach((value) => {
                params.append(`${key}[]`, value);
              });
            });

          console.log("Fetching products with params:", params.toString());

          const response = await apiClient.get(
            `/api/products?${params.toString()}`,
            {
              signal: abortController.signal,
            }
          );

          const { items, pagination } = response.data.data.products;

          if (!abortController.signal.aborted) {
            set({
              products: items,
              currentPage: pagination.current_page,
              totalPages: pagination.last_page,
              filters: response.data.data.filters || state.filters,
              activeFilters:
                response.data.data.active_filters || state.activeFilters,
              lastFetchTime: Date.now(),
              isLoading: false,
              error: null,
              abortController: null,
            });
          }
        } catch (error) {
          if (error.name === "AbortError" || error.name === "CanceledError") {
            console.log("Request was cancelled");
            return;
          }

          console.error("Failed to fetch products:", error);
          set({
            products: [],
            isLoading: false,
            error:
              error.response?.data?.message ||
              error.message ||
              "Failed to fetch products",
            abortController: null,
          });
        }
      },



      debouncedUpdateFilter: (type, values, delay = 150) => {
        const state = get();

        if (state.debounceTimer) {
          clearTimeout(state.debounceTimer);
        }

        const timer = setTimeout(() => {
          const newFilters = { ...get().activeFilters, [type]: values };
          set({ activeFilters: newFilters });
          get().fetchProducts(1, newFilters);
        }, delay);

        set({ debounceTimer: timer });
      },

      updateFilter: (type, values) => {
        const newFilters = {
          ...get().activeFilters,
          [type]: values,
        };
        set({ activeFilters: newFilters });

        get().debouncedUpdateFilter(type, values);
      },

      setCategory: (category) => {
        const state = get();
        const filterKeys = state.categoryFilters[category] || [];
        const emptyFilters = {};
        const emptyActiveFilters = {};

        filterKeys.forEach((key) => {
          emptyFilters[key] = {};
          emptyActiveFilters[key] = [];
        });

        set({
          selectedCategory: category,
          filters: emptyFilters,
          activeFilters: emptyActiveFilters,
        });
        get().fetchProducts(1);
      },

      clearFilters: () => {
        const state = get();

        if (state.debounceTimer) {
          clearTimeout(state.debounceTimer);
          set({ debounceTimer: null });
        }

        const filterKeys = get().getActiveFilterKeys();
        const clearedFilters = {};
        filterKeys.forEach((key) => {
          clearedFilters[key] = [];
        });

        set({ activeFilters: clearedFilters });
        get().fetchProducts(1, clearedFilters);
      },

      reset: () => {
        const state = get();

        if (state.abortController) {
          state.abortController.abort();
        }

        if (state.debounceTimer) {
          clearTimeout(state.debounceTimer);
        }

        set({
          products: [],
          currentPage: 1,
          totalPages: 1,
          perPage: 12,
          isLoading: false,
          error: null,
          filters: {},
          activeFilters: {},
          selectedCategory: "air-pumps",
          abortController: null,
          debounceTimer: null,
          lastFetchTime: null,
        });
      },
    }),
    {
      name: "products-store",
      partialize: (state) => ({
        products: state.products,
        currentPage: state.currentPage,
        totalPages: state.totalPages,
        filters: state.filters,
        activeFilters: state.activeFilters,
        selectedCategory: state.selectedCategory,
        lastFetchTime: state.lastFetchTime,
      }),
    }
  )
);
