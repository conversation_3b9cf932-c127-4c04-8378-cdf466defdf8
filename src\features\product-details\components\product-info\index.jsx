import {
  <PERSON><PERSON><PERSON>t,
  <PERSON>age<PERSON>heck,
  BadgeDollarSign,
  Boxes,
  Layers,
} from "lucide-react";
import { useState } from "react";

// Product Image Component
const ProductImage = ({ src, alt }) => {
  const [loaded, setLoaded] = useState(false);

  return (
    <div className="relative w-full aspect-[4/3] sm:aspect-[3/2] md:aspect-[4/3] lg:aspect-[5/4] xl:aspect-[16/10] overflow-hidden rounded-lg bg-gray-100">
      {!loaded && (
        <div className="absolute inset-0 bg-gray-300 animate-pulse z-10" />
      )}
      <img
        src={src}
        alt={alt}
        className="absolute inset-0 w-full h-full object-cover"
        loading="lazy"
        onLoad={() => setLoaded(true)}
        onError={() => setLoaded(true)}
      />
    </div>
  );
};

export default function ProductInfo({ product, onBack }) {
  const primaryImage =
    product.image ||
    product.media ||
    (product.images && product.images.length > 0
      ? product.images[0]
      : null) ||
    "https://via.placeholder.com/600x400/e5e7eb/9ca3af?text=No+Image+Available";

  const primaryModel =
    product.comparison_table && product.comparison_table.length > 0
      ? product.comparison_table[0]
      : null;

  return (
    <div className="bg-slate-200 px-4 sm:px-6 md:px-10 lg:px-20 py-12">
      <div className="max-w-7xl mx-auto">
        {/* Back Button */}
        <button
          onClick={onBack}
          className="inline-flex items-center gap-2 text-[var(--primary)] font-semibold hover:underline mb-8"
          aria-label="Back to products"
        >
          <ArrowLeft size={20} />
          Back to Products
        </button>

        {/* Product Details Card */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="flex flex-col lg:flex-row">
            {/* Image Section */}
            <div className="lg:w-3/5 p-4 md:p-6">
              <ProductImage src={primaryImage} alt={product.name} />
            </div>

            {/* Info Section */}
            <div className="lg:w-3/5 p-4 md:p-6 lg:pl-10">
              {/* Header */}
              <div className="mb-6">
                <div className="flex items-center gap-3 mb-3">
                  <span className="inline-flex items-center px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded">
                    <div className="w-1.5 h-1.5 bg-green-400 rounded-full mr-1"></div>
                    {product.status === "available"
                      ? "In Stock"
                      : product.status || "Status Unknown"}
                  </span>
                  <span className="text-xs text-gray-500 font-medium capitalize">
                    {product.category}
                  </span>
                </div>

                <h1 className="text-3xl font-bold text-gray-900 mb-3">
                  {product.name}
                </h1>

                <p className="text-gray-600 text-base leading-relaxed">
                  {product.summary || "No description provided."}
                </p>
              </div>

              {/* Product Info Grid */}
              <dl className="grid grid-cols-2 gap-x-6 gap-y-5 text-sm text-gray-700 mb-10">
                {/* Model */}
                <div className="flex items-start gap-3">
                  <PackageCheck className="w-5 h-5 text-blue-600 mt-0.5" />
                  <div>
                    <dt className="text-gray-500 font-medium">Model</dt>
                    <dd className="font-semibold text-gray-900">
                      {primaryModel?.label || "N/A"}
                    </dd>
                  </div>
                </div>

                {/* SKU */}
                <div className="flex items-start gap-3">
                  <BadgeDollarSign className="w-5 h-5 text-purple-600 mt-0.5" />
                  <div>
                    <dt className="text-gray-500 font-medium">SKU</dt>
                    <dd className="font-semibold text-gray-900">
                      {primaryModel?.sku || "N/A"}
                    </dd>
                  </div>
                </div>

                {/* Stock */}
                <div className="flex items-start gap-3">
                  <Boxes className="w-5 h-5 text-green-600 mt-0.5" />
                  <div>
                    <dt className="text-gray-500 font-medium">Stock</dt>
                    <dd className="font-semibold text-gray-900">
                      {primaryModel
                        ? `${primaryModel.stock_quantity} units`
                        : "N/A"}
                    </dd>
                  </div>
                </div>

                {/* Category */}
                <div className="flex items-start gap-3">
                  <Layers className="w-5 h-5 text-indigo-600 mt-0.5" />
                  <div>
                    <dt className="text-gray-500 font-medium">Category</dt>
                    <dd className="font-semibold text-gray-900 capitalize">
                      {product.category || "N/A"}
                    </dd>
                  </div>
                </div>
              </dl>

              {/* Action Buttons }
              {/* <div className="pt-4 border-t">
                <div className="flex flex-col sm:flex-row gap-4">
                  <button className="bg-[var(--primary)] text-white font-semibold py-2 px-4 rounded hover:bg-red-700 transition duration-200">
                    Request Quote
                  </button>
                  <button className="border border-gray-300 text-gray-700 font-semibold py-2 px-4 rounded hover:bg-gray-100 transition duration-200">
                    Add to Wishlist
                  </button>
                </div>
              </div> */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
