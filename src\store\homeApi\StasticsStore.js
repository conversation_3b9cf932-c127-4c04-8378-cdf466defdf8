import { create } from "zustand";
import apiClient from "../../api/axiosConfig.js";

export const useStatisticsStore = create((set) => ({
  StatisticsData: [],
  isLoading: false,
  error: null,

  fetchStatisticsData: async () => {
    set({ isLoading: true, error: null });

    try {
      const response = await apiClient.get("/api/statistics");
      console.log(response);
      const data = response.data.data || [];

      set({
        StatisticsData: data,
        isLoading: false,
        error: null,
      });

      return data;
    } catch (error) {
      console.error("Error fetching statistics data:", error);
      const errorMessage =
        error.response?.data?.message ||
        error.message ||
        "Failed to fetch statistics data";

      set({
        StatisticsData: [],
        isLoading: false,
        error: errorMessage,
      });
      throw error;
    }
  },

  // Clear error
  clearError: () => set({ error: null }),

  // Reset store
  reset: () =>
    set({
      StatisticsData: [],
      isLoading: false,
      error: null,
    }),
}));
