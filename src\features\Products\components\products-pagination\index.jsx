// src/Products/components/products-pagination/ProductsPagination.jsx
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useProductsStore } from "../../../../store/productsApi";


export default function ProductsPagination() {
  const {
    currentPage,
    totalPages,
    fetchProducts,
    activeFilters,
  } = useProductsStore();

  const goToPage = (page) => {
    if (page >= 1 && page <= totalPages) {
      fetchProducts(page, activeFilters);
    }
  };

  if (totalPages <= 1) return null;

  return (
    <div className="flex justify-center mt-8">
      <nav className="inline-flex gap-1">
        {/* Previous */}
        <button
          onClick={() => goToPage(currentPage - 1)}
          disabled={currentPage === 1}
          className="flex items-center gap-1 px-3 py-2 border rounded-md text-sm font-medium 
                     text-gray-700 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronLeft size={16} />
          Prev
        </button>

        {/* Page Numbers */}
        {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
          <button
            key={page}
            onClick={() => goToPage(page)}
            className={`px-3 py-2 border rounded-md text-sm font-medium ${
              currentPage === page
                ? "bg-[var(--primary)] text-white border-[var(--primary)]"
                : "text-gray-700 hover:bg-gray-100"
            }`}
          >
            {page}
          </button>
        ))}

        {/* Next */}
        <button
          onClick={() => goToPage(currentPage + 1)}
          disabled={currentPage === totalPages}
          className="flex items-center gap-1 px-3 py-2 border rounded-md text-sm font-medium 
                     text-gray-700 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Next
          <ChevronRight size={16} />
        </button>
      </nav>
    </div>
  );
}
