//? Export all stores from their individual files
export { useHeroStore } from "./homeApi/heroStore";
export { useStatisticsStore } from "./homeApi/StasticsStore";
// export { useWorkshopStore } from "./homeApi/workshopStore";

//? You can also create a combined hook if needed for loading all home page data
import { useHeroStore } from "./homeApi/heroStore";
import { useStatisticsStore } from "./homeApi/StasticsStore";
// import { useWorkshopStore } from "./homeApi/workshopStore";

export const useHomePageData = () => {
  const heroStore = useHeroStore();
  const statisticsStore = useStatisticsStore();
  //   const workshopStore = useWorkshopStore();
  const fetchAllHomeData = async () => {
    try {
      await Promise.all([
        heroStore.fetchHeroData(),
        statisticsStore.fetchStatisticsData(),
        // workshopStore.fetchWorkshopData(),
      ]);
    } catch (error) {
      console.error("Error fetching home page data:", error);
    }
  };

  const isLoading = heroStore.isLoading || statisticsStore.isLoading;

  // const isLoading = heroStore.isLoading || statisticsStore.isLoading || workshopStore.isLoading

  return {
    fetchAllHomeData,
    isLoading,
    heroStore,
    statisticsStore,
    // workshopStore,
  };
};
