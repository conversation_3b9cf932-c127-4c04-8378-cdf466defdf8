import { <PERSON>u, X, <PERSON>, PhoneCall } from "lucide-react";
import { useState, useRef, useEffect } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";

export default function Header() {
  const navLinks = [
    { name: "Home", path: "/" },
    { name: "About", path: "/about" },
    { name: "Products", path: "/products" },
    { name: "News", path: "/news" },
    // { name: "Applications", path: "/applications" },
    // { name: "Contact", path: "/contact" },
  ];

  const [isOpen, setIsOpen] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const location = useLocation();
  const navigate = useNavigate();
  const searchInputRef = useRef(null);

  const isActive = (path) => location.pathname === path;

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/products?search=${encodeURIComponent(searchQuery.trim())}`);
      setShowSearch(false);
    }
  };

  // Close search when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        searchInputRef.current &&
        !searchInputRef.current.contains(event.target)
      ) {
        setShowSearch(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <header className="bg-white shadow-md sticky top-0 z-50">
      <div className="navbar px-4 lg:px-8 max-w-screen-2xl mx-auto flex justify-between items-center relative">
        {/* Logo */}
        <div className="flex items-center gap-2">
          <span className="text-[var(--primary)] text-3xl font-extrabold">
            PY
          </span>
          <div className="leading-tight">
            <h1 className="text-xl font-bold text-black">Pyramid Power</h1>
            <p className="text-[0.7rem] text-gray-500 font-medium">
              hafez compressors
            </p>
          </div>
        </div>

        {/* Desktop Nav - Centered */}
        <div className="hidden lg:flex absolute left-1/2 transform -translate-x-1/2">
          <ul className="flex gap-6 text-[0.95rem] font-medium">
            {navLinks.map((link) => (
              <li key={link.name}>
                <Link
                  to={link.path}
                  className={`hover:text-[var(--primary)] transition relative ${
                    isActive(link.path)
                      ? "text-[var(--primary)] font-semibold"
                      : "text-black"
                  }`}
                >
                  {link.name}
                  <span
                    className={`absolute left-0 -bottom-1 h-0.5 bg-[var(--primary)] transition-all duration-300 ${
                      isActive(link.path) ? "w-full" : "w-0 group-hover:w-full"
                    }`}
                  ></span>
                </Link>
              </li>
            ))}
          </ul>
        </div>

        {/* Right side - Search, Contact Info & Mobile Menu */}
        <div className="flex items-center gap-4">
          {/* Search - Desktop */}
          <div className="hidden lg:block relative" ref={searchInputRef}>
            <button
              onClick={() => setShowSearch(!showSearch)}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <Search className="w-5 h-5 text-gray-600" />
            </button>

            {showSearch && (
              <div className="absolute right-0 top-full mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                <form onSubmit={handleSearchSubmit} className="p-4">
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Search products..."
                      value={searchQuery}
                      onChange={handleSearchChange}
                      className="w-full pl-10 pr-16 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent"
                      autoFocus
                    />
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <button
                      type="submit"
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-[var(--primary)] text-white px-3 py-1 rounded text-sm hover:bg-[var(--primary)]/90 transition-colors"
                    >
                      Enter
                    </button>
                  </div>
                </form>
              </div>
            )}
          </div>

          {/* Contact Info - Desktop only */}
          <div className="hidden lg:flex items-center gap-2 text-sm">
            <PhoneCall className="text-[var(--primary)] w-5 h-5" />
            <div className="leading-tight">
              <p className="text-gray-500 text-xs">Get A Quote</p>
              <a
                href="mailto:<EMAIL>"
                className="text-black hover:text-[var(--primary)] font-medium duration-400"
              >
                <EMAIL>
              </a>
            </div>
          </div>

          {/* Mobile Menu Button */}
          <div className="lg:hidden flex items-center gap-2">
            <button
              onClick={() => setShowSearch(!showSearch)}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <Search className="w-5 h-5 text-gray-600" />
            </button>
            <button className="btn btn-ghost" onClick={() => setIsOpen(true)}>
              <Menu className="w-6 h-6" />
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Search Bar */}
      {showSearch && (
        <div className="lg:hidden border-t border-gray-200 bg-white p-4">
          <form onSubmit={handleSearchSubmit}>
            <div className="relative">
              <input
                type="text"
                placeholder="Search products..."
                value={searchQuery}
                onChange={handleSearchChange}
                className="w-full pl-10 pr-16 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <button
                type="submit"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-[var(--primary)] text-white px-3 py-1 rounded text-sm hover:bg-[var(--primary)]/90 transition-colors"
              >
                Enter
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Rest of the existing mobile menu code... */}
      <div
        className={`fixed inset-0 z-40 transition-all duration-300 ease-in-out ${
          isOpen
            ? "backdrop-blur-sm bg-opacity-40 pointer-events-auto"
            : "backdrop-blur-none bg-opacity-0 pointer-events-none"
        }`}
        onClick={() => setIsOpen(false)}
      />

      <div
        className={`fixed top-0 right-0 h-full bg-white shadow-2xl z-50 transform transition-all duration-300 ease-in-out ${
          isOpen ? "translate-x-0 opacity-100" : "translate-x-full opacity-0"
        } w-[75%] sm:w-[60%] md:w-[40%] lg:w-[30%]`}
      >
        <div className="flex items-center justify-between p-6 border-b border-base-200">
          <h2 className="text-xl font-bold text-[var(--primary)]">Menu</h2>
          <button
            className="btn btn-sm btn-circle btn-ghost hover:bg-base-200"
            onClick={() => setIsOpen(false)}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <nav className="flex flex-col p-6 space-y-2">
          {navLinks.map((link) => (
            <Link
              key={link.name}
              to={link.path}
              onClick={() => setIsOpen(false)}
              className={`group flex items-center py-4 px-4 rounded-lg text-lg font-medium
                hover:bg-[var(--primary)] hover:text-white transition-all duration-200
                ${
                  isActive(link.path)
                    ? "bg-[var(--primary)]/10 text-[var(--primary)]"
                    : "text-black"
                }`}
            >
              {link.name}
            </Link>
          ))}
        </nav>
      </div>
    </header>
  );
}
