import { create } from 'zustand';
import apiClient from '../../api/axiosConfig.js';

export const useHeroStore = create((set, get) => ({
  // State
  heroData: [],
  isLoading: false,
  error: null,

  // Actions
  fetchHeroData: async () => {
    const state = get();
    
    // Return early if we already have data and not currently loading
    if (state.heroData.length > 0 && !state.isLoading) {
      console.log('Using cached hero data');
      return state.heroData;
    }

    // Prevent multiple simultaneous requests
    if (state.isLoading) {
      console.log('Request already in progress');
      return state.heroData;
    }

    set({ isLoading: true, error: null });
    
    try {
      const response = await apiClient.get('/api/hero-section');
      const data = response.data.data || [];
      
      set({ 
        heroData: data,
        isLoading: false,
        error: null 
      });
      
      return data;
    } catch (error) {
      console.error('Error fetching hero data:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch hero data';
      
      set({ 
        heroData: [],
        isLoading: false,
        error: errorMessage 
      });
      throw error;
    }
  },

  // Clear error
  clearError: () => set({ error: null }),

  // Reset store
  reset: () => set({ 
    heroData: [],
    isLoading: false,
    error: null 
  }),
}));
