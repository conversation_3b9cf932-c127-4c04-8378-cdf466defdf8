import { Lightbulb, Target, Heart, CheckCircle } from "lucide-react";

const COMPANY_VALUES = [
  {
    icon: <Target className="w-7 h-7" />,
    title: "Our Mission",
    tagline: "Empowering Industries",
    desc: "Empowering industries worldwide with innovative air solutions that boost productivity and reliability.",
  },
  {
    icon: <Lightbulb className="w-7 h-7" />,
    title: "Our Vision",
    tagline: "Leading with Innovation",
    desc: "Becoming the global leader in air compression through relentless innovation and outstanding customer care.",
  },
  {
    icon: <Heart className="w-7 h-7" />,
    title: "Core Values",
    tagline: "What Drives Us",
    desc: "Integrity, excellence, innovation, customer focus, and sustainability — the pillars that drive our success daily.",
  },
];

export default function Mission() {
  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 bg-transparent">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6 mb-16">
          <div className="lg:w-1/2">
            <p className="text-xs sm:text-sm uppercase text-red-600 tracking-wider font-semibold mb-2">
              Our Foundation
            </p>
            <h2 className="text-4xl font-extrabold text-gray-900 leading-tight">
              What Drives Us Forward
            </h2>
          </div>
          <div className="lg:w-1/2">
            <p className="text-lg text-gray-700 leading-relaxed">
              Our mission, vision, and core values shape every step we take — guiding us to innovate, excel, and deliver solutions that make a difference worldwide.
            </p>
          </div>
        </div>

        {/* Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
          {COMPANY_VALUES.map(({ icon, title, tagline, desc }, i) => (
            <div
              key={i}
              className="group p-8 rounded-3xl bg-gradient-to-br from-red-50 via-white to-white border border-red-200 shadow-md hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 cursor-pointer"
            >
              {/* Icon Background */}
              <div className="w-14 h-14 rounded-full bg-gradient-to-tr from-red-400 to-red-600 flex items-center justify-center text-white mb-6 shadow-lg group-hover:from-red-500 group-hover:to-red-700 transition-colors duration-300">
                {icon}
              </div>

              {/* Titles */}
              <h3 className="text-2xl font-bold text-gray-900 mb-1">{title}</h3>
              <p className="text-red-600 font-semibold mb-4 tracking-wide">{tagline}</p>

              {/* Description */}
              <p className="text-gray-700 leading-relaxed mb-6">{desc}</p>

              {/* Footer */}
              <div className="flex items-center gap-3 text-red-600 font-semibold">
                <CheckCircle className="w-5 h-5" />
                <span>Core Foundation</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
