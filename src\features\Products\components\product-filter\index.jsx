import { Check, <PERSON>otate<PERSON>c<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ge, <PERSON><PERSON>, <PERSON>ader2, Filter, Grid3X3, Battery, Settings } from "lucide-react";
import { useProductsStore } from "../../../../store/productsApi";

export default function ProductFilter() {
  const {
    filters,
    activeFilters,
    updateFilter,
    clearFilters,
    isLoading,
    selectedCategory,
    categories,
    setCategory,
    getActiveFilterKeys,
  } = useProductsStore();

  const handleFilterClick = (type, value) => {
    const current = activeFilters[type] || [];
    const updated = current.includes(value)
      ? current.filter((v) => v !== value)
      : [...current, value];
    updateFilter(type, updated);
  };

  const clearAllFilters = () => {
    clearFilters();
  };

  const hasActiveFilters = Object.values(activeFilters).some(filterArray => filterArray?.length > 0);
  const activeFiltersCount = Object.values(activeFilters).reduce((count, filterArray) => count + (filterArray?.length || 0), 0);

  const renderSection = (type, label, icon, values) => (
    <div key={type} className="border-t pt-4 first:border-t-0 first:pt-0">
      <h4 className="text-sm font-semibold text-gray-800 mb-3 uppercase tracking-wide flex items-center gap-2">
        {icon}
        {label}
        {isLoading && <Loader2 size={14} className="animate-spin text-blue-500" />}
      </h4>
      <div className="flex flex-wrap gap-2">
        {Object.entries(values).map(([key, label]) => {
          const active = activeFilters[type]?.includes(key);
          return (
            <button
              key={key}
              onClick={() => handleFilterClick(type, key)}
              className={`px-3 py-1.5 rounded-full text-sm border transition-all min-w-fit ${
                active
                  ? "bg-[var(--primary)] text-white border-transparent shadow-sm"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300"
              }`}
            >
              <div className="flex items-center gap-1 justify-center min-w-0">
                <span className="whitespace-nowrap">{label}</span>
                <div className="w-[14px] flex justify-center">
                  {active && <Check size={14} />}
                </div>
              </div>
            </button>
          );
        })}
      </div>
    </div>
  );

  // Dynamic sections based on selected category
  const getSections = () => {
    const filterKeys = getActiveFilterKeys();
    const iconMap = {
      // Air Compressors
      power: { icon: <Zap size={16} />, label: "Power" },
      tank_capacity: { icon: <Battery size={16} />, label: "Tank Capacity" },
      max_pressure: { icon: <Gauge size={16} />, label: "Max Pressure" },
      
      // Electric Motors
      output: { icon: <Zap size={16} />, label: "Output" },
      speed: { icon: <Gauge size={16} />, label: "Speed" },
      efficiency: { icon: <Settings size={16} />, label: "Efficiency" },
      
      // Air Pumps
      rpm: { icon: <Gauge size={16} />, label: "RPM" },
      displacement: { icon: <Wrench size={16} />, label: "Displacement" },
      motor_hp: { icon: <Zap size={16} />, label: "Motor HP" },
    };

    return filterKeys.map(key => ({
      type: key,
      label: iconMap[key]?.label || key,
      icon: iconMap[key]?.icon || <Settings size={16} />
    }));
  };

  const sections = getSections();

  const FilterContent = () => (
    <div className="space-y-6">
      {/* Category Selection */}
      <div className="border-b pb-4">
        <h4 className="text-sm font-semibold text-gray-800 mb-3 uppercase tracking-wide flex items-center gap-2">
          <Grid3X3 size={16} />
          Category
        </h4>
        <div className="space-y-2">
          {categories.map((category) => (
            <button
              key={category.value}
              onClick={() => setCategory(category.value)}
              className={`w-full text-left px-3 py-2 rounded-lg text-sm border transition-all ${
                selectedCategory === category.value
                  ? "bg-[var(--primary)] text-white border-transparent shadow-sm"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300"
              }`}
            >
              <div className="flex items-center justify-between">
                <span>{category.label}</span>
                {selectedCategory === category.value && <Check size={14} />}
              </div>
            </button>
          ))}
        </div>
      </div>

      {sections.map(({ type, label, icon }) =>
        renderSection(type, label, icon, filters[type] || [])
      )}
      
      {hasActiveFilters && (
        <div className="pt-4 border-t">
          <button
            onClick={clearAllFilters}
            disabled={isLoading}
            className="btn btn-outline btn-error btn-sm w-full"
          >
            <RotateCcw size={14} />
            Clear All Filters
          </button>
        </div>
      )}
    </div>
  );

  return (
    <>
      {/* DESKTOP VERSION */}
      <div className="hidden lg:block bg-white rounded-xl shadow-md overflow-hidden sticky top-[calc(var(--navbar-height)+1.5rem)] max-h-[calc(100vh-var(--navbar-height)-3rem)]">
        <div className="p-6 border-b bg-white">
          <h3 className="text-lg font-semibold text-[#1c1a30] flex items-center gap-2">
            <Wrench size={20} />
            Filters
            {isLoading && <Loader2 size={16} className="animate-spin text-blue-500" />}
          </h3>
        </div>
        <div className="p-6 overflow-y-auto max-h-[calc(100vh-var(--navbar-height)-12rem)]">
          <FilterContent />
        </div>
      </div>

      {/* MOBILE VERSION - DaisyUI Drawer */}
      <div className="lg:hidden">
        <div className="drawer drawer-end">
          <input id="filter-drawer" type="checkbox" className="drawer-toggle" />
          
          {/* Floating Filter Button */}
          <div className="drawer-content">
            <label 
              htmlFor="filter-drawer" 
              className="btn bg-[var(--primary)] text-white  btn-circle fixed bottom-6 right-6 z-40 shadow-lg drawer-button"
            >
              <div className="relative">
                <Filter size={24} />
                {activeFiltersCount > 0 && (
                  <div className="badge badge-error badge-sm absolute -top-2 -right-2">
                    {activeFiltersCount}
                  </div>
                )}
              </div>
            </label>
          </div>
          
          {/* Drawer Sidebar */}
          <div className="drawer-side z-50">
            <label htmlFor="filter-drawer" className="drawer-overlay"></label>
            <aside className="min-h-full w-80 max-w-[85vw] bg-base-100 flex flex-col">
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b bg-base-200">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <Filter size={20} />
                  Filters
                  {activeFiltersCount > 0 && (
                    <div className="badge badge-primary badge-sm">
                      {activeFiltersCount}
                    </div>
                  )}
                  {isLoading && <Loader2 size={16} className="animate-spin text-blue-500" />}
                </h3>
                <label htmlFor="filter-drawer" className="btn btn-sm btn-circle btn-ghost">
                  ✕
                </label>
              </div>
              
              {/* Content */}
              <div className="flex-1 overflow-y-auto p-4">
                <FilterContent />
              </div>
            </aside>
          </div>
        </div>
      </div>
    </>
  );
}
