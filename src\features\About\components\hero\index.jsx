import { Calendar, MapPin, Phone } from "lucide-react";
import product1Img from "../../../../assets/images/workerImg2.webp";

// Company stats data
const COMPANY_STATS = [
  { number: "20+", label: "Years Experience" },
  { number: "500+", label: "Projects Completed" },
  { number: "50+", label: "Countries Served" },
  { number: "24/7", label: "Support Available" },
];

export default function Hero() {
  return (
    <div className="relative h-[70vh] w-full overflow-hidden">
      <img
        src={product1Img}
        alt="Pyramid Power facility"
        className="w-full h-full object-cover"
      />

      {/* Dark gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-black/80 via-black/60 to-transparent" />

      {/* Content */}
      <div className="absolute inset-0 flex items-center px-5 sm:px-6 md:px-8 lg:px-16 xl:px-20">
        <div className="max-w-4xl text-white space-y-6 drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)]">
          <p className="text-sm sm:text-base uppercase text-[var(--primary)] tracking-wider font-medium drop-shadow">
            About Pyramid Power
          </p>
          <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold leading-tight drop-shadow-md">
            Leading Industrial Air Solutions with 20+ Years of Excellence
          </h1>
          <p className="text-lg sm:text-xl text-gray-200 max-w-2xl leading-relaxed drop-shadow-sm">
            Pyramid Power delivers premium electric air pumps and custom manufacturing services to clients worldwide.
          </p>

          {/* Stats Row */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 pt-8">
            {COMPANY_STATS.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-[var(--primary)] drop-shadow-md">
                  {stat.number}
                </div>
                <div className="text-sm text-gray-300 mt-1 drop-shadow-sm">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
